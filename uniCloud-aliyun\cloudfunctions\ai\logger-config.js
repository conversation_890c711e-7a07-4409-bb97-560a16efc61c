/**
 * 日志系统配置文件
 * 用于集中管理日志记录系统的配置
 */

const { globalLogger } = require('./modules/utils')

/**
 * 日志配置预设
 */
const LOG_PRESETS = {
  // 开发环境配置
  development: {
    mode: 'specific',
    specificModules: [
      'chatStreamSSE',
      'planner',
      'executor',
      'performance',
      'context',
      'resolver',
      'validator',
      'todo',
      'error-handler'
    ],
    level: 'debug',
    enabled: true,
    showStack: true,
    maxLogs: 1000
  },
  
  // 生产环境配置
  production: {
    mode: 'specific',
    specificModules: [
      'chatStreamSSE',
      'planner',
      'executor'
    ],
    level: 'warn',
    enabled: true,
    showStack: false,
    maxLogs: 500
  },
  
  // 调试模式配置
  debug: {
    mode: 'all',
    specificModules: [],
    level: 'debug',
    enabled: true,
    showStack: true,
    maxLogs: 2000
  },
  
  // 关闭日志配置
  disabled: {
    mode: 'specific',
    specificModules: [],
    level: 'error',
    enabled: false,
    showStack: false,
    maxLogs: 0
  },
  
  // 仅错误日志配置
  errorOnly: {
    mode: 'all',
    specificModules: [],
    level: 'error',
    enabled: true,
    showStack: true,
    maxLogs: 200
  }
}

/**
 * 应用日志配置预设
 * @param {string} presetName - 预设名称
 */
function applyLogPreset(presetName) {
  const preset = LOG_PRESETS[presetName]
  if (!preset) {
    console.warn(`未找到日志预设: ${presetName}`)
    return false
  }
  
  globalLogger.updateConfig(preset)
  console.log(`已应用日志预设: ${presetName}`)
  console.log('当前配置:', globalLogger.config)
  return true
}

/**
 * 动态调整日志配置
 * @param {Object} config - 配置对象
 */
function updateLogConfig(config) {
  globalLogger.updateConfig(config)
  console.log('日志配置已更新:', globalLogger.config)
}

/**
 * 启用特定模块的日志
 * @param {string|Array} modules - 模块名称或模块名称数组
 */
function enableModules(modules) {
  const moduleList = Array.isArray(modules) ? modules : [modules]
  const currentModules = globalLogger.config.specificModules || []
  const newModules = [...new Set([...currentModules, ...moduleList])]
  
  globalLogger.updateConfig({
    specificModules: newModules,
    mode: 'specific'
  })
  
  console.log(`已启用模块日志: ${moduleList.join(', ')}`)
  console.log('当前启用的模块:', newModules)
}

/**
 * 禁用特定模块的日志
 * @param {string|Array} modules - 模块名称或模块名称数组
 */
function disableModules(modules) {
  const moduleList = Array.isArray(modules) ? modules : [modules]
  const currentModules = globalLogger.config.specificModules || []
  const newModules = currentModules.filter(module => !moduleList.includes(module))
  
  globalLogger.updateConfig({
    specificModules: newModules
  })
  
  console.log(`已禁用模块日志: ${moduleList.join(', ')}`)
  console.log('当前启用的模块:', newModules)
}

/**
 * 获取日志统计信息
 * @returns {Object} 统计信息
 */
function getLogStats() {
  const report = globalLogger.generateReport()
  const config = globalLogger.config
  
  return {
    config,
    report,
    recentLogs: globalLogger.getLogs({ since: Date.now() - 60000 }), // 最近1分钟的日志
    errorLogs: globalLogger.getLogs({ level: 'error' }) // 所有错误日志
  }
}

/**
 * 清空日志历史
 */
function clearLogs() {
  globalLogger.clearLogs()
  console.log('日志历史已清空')
}

/**
 * 导出日志到文件（模拟）
 * @param {Object} options - 导出选项
 */
function exportLogs(options = {}) {
  const {
    module = null,
    level = null,
    since = null,
    format = 'json'
  } = options
  
  const logs = globalLogger.getLogs({ module, level, since })
  
  if (format === 'json') {
    const exportData = {
      exportTime: new Date().toISOString(),
      config: globalLogger.config,
      totalLogs: logs.length,
      logs
    }
    
    console.log('日志导出数据 (JSON格式):')
    console.log(JSON.stringify(exportData, null, 2))
  } else if (format === 'text') {
    console.log('日志导出数据 (文本格式):')
    logs.forEach(log => {
      const timestamp = new Date(log.timestamp).toISOString()
      console.log(`[${timestamp}] [${log.level}] [${log.module}] ${log.message}`)
      if (log.data) {
        console.log('  数据:', JSON.stringify(log.data))
      }
    })
  }
  
  return logs
}

// 根据环境变量自动应用配置
function autoConfigureLogger() {
  // 这里可以根据实际的环境变量或配置来决定使用哪个预设
  // 目前默认使用开发环境配置
  const env = process.env.NODE_ENV || 'development'
  const preset = LOG_PRESETS[env] || LOG_PRESETS.development
  
  globalLogger.updateConfig(preset)
  console.log(`自动应用日志配置: ${env}`)
}

// 初始化时自动配置
autoConfigureLogger()

module.exports = {
  LOG_PRESETS,
  applyLogPreset,
  updateLogConfig,
  enableModules,
  disableModules,
  getLogStats,
  clearLogs,
  exportLogs,
  autoConfigureLogger
}

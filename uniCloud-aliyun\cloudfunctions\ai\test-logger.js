/**
 * 日志系统测试文件
 * 用于验证统一日志记录系统的功能
 */

const { createLogger, globalLogger } = require('./modules/utils')

// 测试日志系统
async function testLoggerSystem() {
  console.log('========== 开始测试日志系统 ==========')
  
  // 创建不同模块的日志记录器
  const chatLogger = createLogger('chatStreamSSE')
  const plannerLogger = createLogger('planner')
  const executorLogger = createLogger('executor')
  const testLogger = createLogger('test-module')
  
  // 测试基本日志功能
  console.log('\n1. 测试基本日志功能:')
  chatLogger.info('这是一条信息日志', { key: 'value', number: 123 })
  chatLogger.warn('这是一条警告日志', { warning: 'test warning' })
  chatLogger.error('这是一条错误日志', new Error('测试错误'), { context: 'error context' })
  
  // 测试函数入口和出口日志
  console.log('\n2. 测试函数入口和出口日志:')
  chatLogger.enter('testFunction', { param1: 'value1', param2: 123 })
  chatLogger.step('处理参数', { processedParam: 'processed' })
  chatLogger.exit('testFunction', { result: 'success', data: [1, 2, 3] })
  
  // 测试错误出口日志
  console.log('\n3. 测试错误出口日志:')
  try {
    throw new Error('模拟函数执行错误')
  } catch (error) {
    chatLogger.exitError('testFunction', error)
  }
  
  // 测试不同模块的日志（应该被过滤）
  console.log('\n4. 测试模块过滤（以下日志应该不会显示）:')
  testLogger.info('这条日志应该被过滤掉', { reason: 'test-module 不在 specificModules 列表中' })
  
  // 测试 planner 和 executor 模块（被注释掉，应该不显示）
  plannerLogger.info('这条 planner 日志应该被过滤掉')
  executorLogger.info('这条 executor 日志应该被过滤掉')
  
  // 测试日志配置更新
  console.log('\n5. 测试日志配置更新:')
  console.log('当前配置:', globalLogger.config)
  
  // 临时启用所有模块
  globalLogger.updateConfig({
    mode: 'all'
  })
  
  console.log('更新配置为全量模式后:')
  testLogger.info('现在这条日志应该显示了', { mode: 'all' })
  plannerLogger.info('planner 日志现在也应该显示')
  executorLogger.info('executor 日志现在也应该显示')
  
  // 恢复原配置
  globalLogger.updateConfig({
    mode: 'specific'
  })
  
  // 测试日志历史和报告
  console.log('\n6. 测试日志历史和报告:')
  const logs = globalLogger.getLogs({ module: 'chatStreamSSE' })
  console.log(`chatStreamSSE 模块的日志数量: ${logs.length}`)
  
  const report = globalLogger.generateReport()
  console.log('日志统计报告:', report)
  
  // 测试复杂对象的日志记录
  console.log('\n7. 测试复杂对象日志记录:')
  const complexObject = {
    user: {
      id: 123,
      name: 'test user',
      password: 'secret123', // 应该被隐藏
      token: 'abc123def', // 应该被隐藏
      profile: {
        age: 25,
        hobbies: ['reading', 'coding', 'music'],
        settings: {
          theme: 'dark',
          notifications: true
        }
      }
    },
    data: new Array(15).fill(0).map((_, i) => ({ id: i, value: `item-${i}` })), // 测试数组截断
    timestamp: new Date(),
    error: new Error('测试错误对象')
  }
  
  chatLogger.info('复杂对象日志测试', complexObject)
  
  console.log('\n========== 日志系统测试完成 ==========')
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testLoggerSystem().catch(console.error)
}

module.exports = {
  testLoggerSystem
}

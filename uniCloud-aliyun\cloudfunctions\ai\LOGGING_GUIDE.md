# 统一日志记录系统使用指南

## 概述

本项目实现了一个统一的日志记录系统，为 `chatStreamSSE` 函数及其相关模块提供详细的日志记录功能。该系统支持模块化日志管理、多种日志级别、敏感信息过滤等功能。

## 核心特性

### 1. 模块化日志管理
- 支持按模块名称过滤日志输出
- 两种模式：指定模块模式和全量模式
- 可动态启用/禁用特定模块的日志

### 2. 多级别日志记录
- `debug`: 调试信息
- `info`: 一般信息
- `warn`: 警告信息
- `error`: 错误信息

### 3. 智能数据处理
- 自动清理敏感信息（密码、令牌等）
- 防止循环引用
- 限制对象深度和数组长度

### 4. 性能友好
- 可配置的日志级别
- 内存使用控制
- 生产环境优化

## 快速开始

### 基本使用

```javascript
const { createLogger } = require('./modules/utils')

// 创建模块日志记录器
const logger = createLogger('myModule')

// 记录不同级别的日志
logger.debug('调试信息', { data: 'debug data' })
logger.info('一般信息', { user: 'john', action: 'login' })
logger.warn('警告信息', { warning: 'deprecated API' })
logger.error('错误信息', new Error('something went wrong'), { context: 'additional info' })
```

### 函数生命周期日志

```javascript
// 函数入口
logger.enter('functionName', { param1: 'value1', param2: 123 })

// 关键步骤
logger.step('处理数据', { processedCount: 100 })

// 正常出口
logger.exit('functionName', { result: 'success', data: results })

// 异常出口
logger.exitError('functionName', error)
```

## 配置管理

### 使用预设配置

```javascript
const { applyLogPreset } = require('./logger-config')

// 应用开发环境配置
applyLogPreset('development')

// 应用生产环境配置
applyLogPreset('production')

// 应用调试模式配置
applyLogPreset('debug')
```

### 动态配置调整

```javascript
const { updateLogConfig, enableModules, disableModules } = require('./logger-config')

// 更新配置
updateLogConfig({
  level: 'info',
  showStack: false
})

// 启用特定模块
enableModules(['planner', 'executor'])

// 禁用特定模块
disableModules(['performance'])
```

## 配置选项

### 日志模式
- `specific`: 只记录指定模块的日志
- `all`: 记录所有模块的日志

### 日志级别
- `debug`: 记录所有级别的日志
- `info`: 记录 info、warn、error 级别的日志
- `warn`: 记录 warn、error 级别的日志
- `error`: 只记录 error 级别的日志

### 其他配置
- `enabled`: 是否启用日志记录
- `showStack`: 是否显示错误堆栈信息
- `maxLogs`: 最大日志条数（防止内存溢出）

## 预设配置

### development（开发环境）
```javascript
{
  mode: 'specific',
  specificModules: ['chatStreamSSE', 'planner', 'executor', ...],
  level: 'debug',
  enabled: true,
  showStack: true,
  maxLogs: 1000
}
```

### production（生产环境）
```javascript
{
  mode: 'specific',
  specificModules: ['chatStreamSSE', 'planner', 'executor'],
  level: 'warn',
  enabled: true,
  showStack: false,
  maxLogs: 500
}
```

### debug（调试模式）
```javascript
{
  mode: 'all',
  level: 'debug',
  enabled: true,
  showStack: true,
  maxLogs: 2000
}
```

## 日志分析

### 获取统计信息

```javascript
const { getLogStats } = require('./logger-config')

const stats = getLogStats()
console.log('配置:', stats.config)
console.log('统计报告:', stats.report)
console.log('最近日志:', stats.recentLogs)
console.log('错误日志:', stats.errorLogs)
```

### 导出日志

```javascript
const { exportLogs } = require('./logger-config')

// 导出所有日志
exportLogs({ format: 'json' })

// 导出特定模块的日志
exportLogs({ module: 'chatStreamSSE', format: 'text' })

// 导出最近的错误日志
exportLogs({ 
  level: 'error', 
  since: Date.now() - 3600000, // 最近1小时
  format: 'json' 
})
```

## 测试日志系统

运行测试文件验证日志系统功能：

```bash
node test-logger.js
```

## 最佳实践

### 1. 模块命名规范
- 使用清晰的模块名称
- 保持命名一致性
- 避免过长的模块名

### 2. 日志内容规范
- 提供有意义的日志消息
- 包含必要的上下文信息
- 避免记录敏感信息

### 3. 性能考虑
- 在生产环境中适当调整日志级别
- 定期清理日志历史
- 监控内存使用情况

### 4. 错误处理
- 始终记录错误的完整信息
- 提供足够的上下文帮助调试
- 使用 exitError 方法记录函数异常

## 故障排查

### 日志不显示
1. 检查模块是否在 `specificModules` 列表中
2. 确认日志级别设置是否正确
3. 验证 `enabled` 配置是否为 true

### 性能问题
1. 调整 `maxLogs` 限制日志数量
2. 提高日志级别减少输出
3. 在生产环境中禁用 debug 日志

### 内存占用过高
1. 定期调用 `clearLogs()` 清空历史
2. 降低 `maxLogs` 设置
3. 检查是否有循环引用导致的内存泄漏

## 扩展功能

系统支持进一步扩展：
- 添加日志持久化存储
- 集成外部日志服务
- 实现日志告警机制
- 添加日志可视化界面
